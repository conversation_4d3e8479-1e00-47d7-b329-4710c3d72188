version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-secure_password_123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-whodb_test}
      MYSQL_USER: ${MYSQL_USER:-whodb_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-whodb_pass_123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  whodb:
    image: clidey/whodb
    ports:
      - "8080:8080"
    environment:
      # 预配置 MySQL 连接
      WHODB_MYSQL_1: '{"host":"mysql","user":"${MYSQL_USER:-whodb_user}","password":"${MYSQL_PASSWORD:-whodb_pass_123}","database":"${MYSQL_DATABASE:-whodb_test}"}'
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
