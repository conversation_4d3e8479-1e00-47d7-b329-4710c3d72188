enum DatabaseType {
  <PERSON>gres,
  MySQL,
  Sqlite3,
  MongoDB,
  Redis,
  ElasticSearch,
  MariaDB,
  ClickHouse,
}

type Column {
  Type: String!
  Name: String!
}

enum WhereConditionType {
  Atomic
  And
  Or
}

input AtomicWhereCondition {
  ColumnType: String!
  Key: String!
  Operator: String!
  Value: String!
}

input OperationWhereCondition {
  Children: [WhereCondition!]!
}

input WhereCondition {
  Type: WhereConditionType!
  Atomic: AtomicWhereCondition
  And: OperationWhereCondition
  Or: OperationWhereCondition
}

type RowsResult {
  Columns: [Column!]!
  Rows: [[String!]!]!
  DisableUpdate: Boolean!
}

type Record {
  Key: String!
  Value: String!
}

input RecordInput {
  Key: String!
  Value: String!
  Extra: [RecordInput!]
}

type StorageUnit {
  Name: String!
  Attributes: [Record!]!
}

enum GraphUnitRelationshipType {
  OneToOne,
  OneToMany,
  ManyToOne,
  ManyToMany,
  Unknown,
}

type GraphUnitRelationship {
  Name: String!
  Relationship: GraphUnitRelationshipType!
}

type GraphUnit {
  Unit: StorageUnit!
	Relations: [GraphUnitRelationship!]!
}

input LoginCredentials {
  Id: String
  Type: String!
  Hostname: String!
  Username: String!
  Password: String!
  Database: String!
  Advanced: [RecordInput!]
}

type SettingsConfig {
  MetricsEnabled: Boolean
}

input SettingsConfigInput {
  MetricsEnabled: String
}

input LoginProfileInput {
  Id: String!
  Type: DatabaseType!
  Database: String
}

type LoginProfile {
  Alias: String
  Id: String!
  Type: DatabaseType!
  Database: String
  IsEnvironmentDefined: Boolean!
}

type StatusResponse {
  Status: Boolean!
}

input ChatInput {
  PreviousConversation: String!
  Query: String!
  Model: String!
  Token: String
}

type AIChatMessage {
  Type: String!
  Result: RowsResult
  Text: String!
}

type AIProvider {
  Type: String!
  ProviderId: String!
  IsEnvironmentDefined: Boolean!
}

type Query {
  Version: String!
  Profiles: [LoginProfile!]!
  Database(type: String!): [String!]!
  Schema: [String!]!
  StorageUnit(schema: String!): [StorageUnit!]!
  Row(schema: String!, storageUnit: String!, where: WhereCondition, pageSize: Int!, pageOffset: Int!): RowsResult!
  RawExecute(query: String!): RowsResult!
  Graph(schema: String!): [GraphUnit!]!
  AIProviders: [AIProvider!]!
  AIModel(providerId: String, modelType: String!, token: String): [String!]!
  AIChat(providerId: String, modelType: String!, token: String, schema: String!, input: ChatInput!): [AIChatMessage!]!
  SettingsConfig: SettingsConfig!
}

type Mutation {
  Login(credentials: LoginCredentials!): StatusResponse!
  LoginWithProfile(profile: LoginProfileInput!): StatusResponse!
  Logout: StatusResponse!
  UpdateSettings(newSettings: SettingsConfigInput!): StatusResponse!

  AddStorageUnit(schema: String!, storageUnit: String!, fields: [RecordInput!]!): StatusResponse!
  UpdateStorageUnit(schema: String!, storageUnit: String!, values: [RecordInput!]!, updatedColumns: [String!]!): StatusResponse!
  AddRow(schema: String!, storageUnit: String!, values: [RecordInput!]!): StatusResponse!
  DeleteRow(schema: String!, storageUnit: String!, values: [RecordInput!]!): StatusResponse!
}
