# Copyright 2025 Clidey, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# WhoDB Enterprise Edition Dockerfile
# Build from project root: docker build -f ee/Dockerfile -t whodb:ee .

# Build frontend with EE modules
FROM node:lts-alpine AS build-stage
RUN npm i -g pnpm
WORKDIR /app

# Copy frontend package files
COPY ./frontend/package.json ./frontend/pnpm-lock.yaml ./
RUN pnpm install

# Copy all frontend source including EE components
COPY ./frontend/ ./
# Copy EE frontend files to the correct location relative to frontend build context
COPY ./ee/ ../ee/

# Build frontend with EE support
RUN pnpm run build:ee

# Build backend with EE modules
FROM golang:1.24.1-alpine AS backend-stage
RUN apk update && apk add --no-cache gcc musl-dev git bash
WORKDIR /app

# Copy scripts first as they're needed for GraphQL generation
COPY ./scripts/ ./scripts/

# Copy core module files
COPY ./core/go.mod ./core/go.sum ./core/
WORKDIR /app/core
RUN go mod download

# Copy EE module files
WORKDIR /app
COPY ./ee/go.mod ./ee/go.sum ./ee/
WORKDIR /app/ee
RUN go mod download

# Copy all source code
WORKDIR /app
COPY ./core/ ./core/
COPY ./ee/ ./ee/

# Copy frontend build to be embedded
COPY --from=build-stage /app/build/ ./core/build/

# Generate GraphQL code for EE
# First ensure we have all the dependencies for gqlgen
WORKDIR /app/ee
RUN go mod download
WORKDIR /app

# Build backend with EE features
WORKDIR /app/core
# Add replace directive for EE module
RUN go mod edit -replace github.com/clidey/whodb/ee=/app/ee && \
    go mod tidy && \
    CGO_ENABLED=1 GOOS=linux go build -tags ee -o /core

# Final stage
FROM alpine:3.22.1
RUN apk update && apk add --no-cache ca-certificates
WORKDIR /app
COPY --from=backend-stage /core /core

CMD ["/core"]