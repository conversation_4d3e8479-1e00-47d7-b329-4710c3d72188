/*
 * Copyright 2025 Clidey, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));
@custom-variant hover (&:hover);

@theme {
  --font-title:
    'Megrim', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;

  --color-brand: #bf853e;

  --animate-fade: fadeOut 300ms ease-in-out;
  --animate-boxy: boxy 1.5s infinite;

  @keyframes fadeOut {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 100;
    }
  }
  @keyframes boxy {
    0% {
      background-size:
        35px 15px,
        15px 15px,
        15px 35px,
        35px 35px;
    }
    25% {
      background-size:
        35px 35px,
        15px 35px,
        15px 15px,
        35px 15px;
    }
    50% {
      background-size:
        15px 35px,
        35px 35px,
        35px 15px,
        15px 15px;
    }
    75% {
      background-size:
        15px 15px,
        35px 15px,
        35px 35px,
        15px 35px;
    }
    100% {
      background-size:
        35px 15px,
        15px 15px,
        15px 35px,
        35px 35px;
    }
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility loader {
  --g1: conic-gradient(
    from 90deg at top 3px left 3px,
    transparent 90deg,
    #666666 0
  );
  --g2: conic-gradient(
    from -90deg at bottom 3px right 3px,
    transparent 90deg,
    #666666 0
  );
  background:
    var(--g1), var(--g1), var(--g1), var(--g1), var(--g2), var(--g2), var(--g2),
    var(--g2);
  background-position:
    0 0,
    100% 0,
    100% 100%,
    0 100%;
  background-repeat: no-repeat;
  animation: boxy 1.5s infinite;

  .dark & {
    --g1: conic-gradient(
      from 90deg at top 3px left 3px,
      transparent 90deg,
      #bbb 0
    );
    --g2: conic-gradient(
      from -90deg at bottom 3px right 3px,
      transparent 90deg,
      #bbb 0
    );
    background:
      var(--g1), var(--g1), var(--g1), var(--g1), var(--g2), var(--g2),
      var(--g2), var(--g2);
    background-position:
      0 0,
      100% 0,
      100% 100%,
      0 100%;
    background-repeat: no-repeat;
    animation: boxy 1.5s infinite;
  }
}

@utility dark {
  & .loader {
    --g1: conic-gradient(
      from 90deg at top 3px left 3px,
      transparent 90deg,
      #bbb 0
    );
    --g2: conic-gradient(
      from -90deg at bottom 3px right 3px,
      transparent 90deg,
      #bbb 0
    );
    background:
      var(--g1), var(--g1), var(--g1), var(--g1), var(--g2), var(--g2),
      var(--g2), var(--g2);
    background-position:
      0 0,
      100% 0,
      100% 100%,
      0 100%;
    background-repeat: no-repeat;
    animation: boxy 1.5s infinite;
  }
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1;
}

/* Scrollbar Track */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background-color: #d1d1d1; /* Light gray color */
  border-radius: 12px; /* Rounded corners */
  border: 1px solid #f5f5f5; /* Lighter border color */
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1); /* Inner shadow effect */
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #b0b0b0; /* Darker gray color on hover */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Track */
::-webkit-scrollbar-track {
  background-color: #f9f9f9; /* Track color */
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1); /* Inner shadow effect */
}

.dark ::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Track on hover */
::-webkit-scrollbar-track:hover {
  background-color: #f2f2f2; /* Darker track color on hover */
}

.dark ::-webkit-scrollbar-track:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.dark .react-flow__controls-button {
  background-color: rgb(255 255 255 / 0.1);
  border-color:  rgb(255 255 255 / 0.1);
  fill: white;
}

.dark .react-flow__controls-button:hover {
  background-color: rgb(255 255 255 / 0.2);
  border-color:  rgb(255 255 255 / 0.2);
}

.markdown-preview > * {
  all: revert;
}
