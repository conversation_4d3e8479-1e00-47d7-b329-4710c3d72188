/**
 * Copyright 2025 Clidey, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Icons } from "../../components/icons";

export const chatExamples = [
    {
        icon: Icons.Database,
        description: "Get summary of all the purchase orders",
    },
    {
        icon: Icons.Users,
        description: "List all customers who made a purchase in the last month",
    },
    {
        icon: Icons.Users,
        description: "Show the top 10 customers by total spend",
    },
    {
        icon: Icons.Feedback,
        description: "Provide a summary of customer feedback for the past year",
    },
    {
        icon: Icons.Sales,
        description: "List all purchase orders placed within the last week",
    },
    {
        icon: Icons.Sales,
        description: "Summarize total sales by region for this quarter",
    },
    {
        icon: Icons.Users,
        description: "Show the average order value for each product category",
    },
    {
        icon: Icons.Users,
        description: "List all products that are currently out of stock",
    },
    {
        icon: Icons.Users,
        description: "Show the top 5 best-selling products in the electronics category",
    },
    {
        icon: Icons.Inventory,
        description: "Provide the total count of different products in each warehouse",
    },
    {
        icon: Icons.Users,
        description: "List all employees who joined in the last six months",
    },
    {
        icon: Icons.Users,
        description: "Get a summary of employee demographics by department",
    },
    {
        icon: Icons.Users,
        description: "Show the average salary for software engineers",
    },
    {
        icon: Icons.Financials,
        description: "Provide a breakdown of expenses by department for the last financial year",
    },
    {
        icon: Icons.Financials,
        description: "Summarize the profit and loss statement for the last quarter",
    },
    {
        icon: Icons.Financials,
        description: "Show the total revenue generated by each sales channel",
    },
    {
        icon: Icons.Marketing,
        description: "Provide a list of all active marketing campaigns",
    },
    {
        icon: Icons.Marketing,
        description: "Summarize the click-through rates of email campaigns for the last quarter",
    },
    {
        icon: Icons.Marketing,
        description: "Show the total number of leads generated by each campaign",
    },
    {
        icon: Icons.Projects,
        description: "List all ongoing projects and their current status",
    },
    {
        icon: Icons.Projects,
        description: "Get a summary of project deadlines and milestones for the next quarter",
    },
    {
        icon: Icons.Projects,
        description: "Show the top 5 projects by budget allocation",
    },
    {
        icon: Icons.Analytics,
        description: "Provide a trend analysis of website traffic for the past six months",
    },
    {
        icon: Icons.Analytics,
        description: "Summarize the conversion rates by traffic source",
    },
    {
        icon: Icons.Analytics,
        description: "Show the top-performing keywords in our search engine marketing campaigns",
    },
    {
        icon: Icons.Compliance,
        description: "List all transactions flagged for audit in the last year",
    },
    {
        icon: Icons.Compliance,
        description: "Summarize compliance issues reported by region",
    },
    {
        icon: Icons.Compliance,
        description: "Show a report of all policy violations in the last quarter",
    },
];