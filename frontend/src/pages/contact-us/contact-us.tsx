/**
 * Copyright 2025 Clidey, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import {FC} from "react";
import {InternalPage} from "../../components/page";
import {InternalRoutes} from "../../config/routes";

export const ContactUsPage: FC = () => {
    return <InternalPage routes={[InternalRoutes.ContactUs!]}>
        <div className="flex justify-center items-center w-full">
            <iframe
                title={"WhoDB Feedback Form"}
                src="https://docs.google.com/forms/d/e/1FAIpQLSfldEyTbzRdtsFX_6fYtntg9N9s_M7zm8wX8JmrOc98IJPX_A/viewform?embedded=true"
                width="100%" height="1500">Loading…
            </iframe>
        </div>
    </InternalPage>
}