{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "^3.13.8", "@codemirror/lang-json": "^6.0.2", "@codemirror/lang-markdown": "^6.3.3", "@codemirror/lang-sql": "^6.9.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.1", "@dagrejs/dagre": "^1.1.5", "@emotion/is-prop-valid": "^1.3.1", "@reduxjs/toolkit": "^2.8.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-table": "^7.7.20", "classnames": "^2.5.1", "codemirror": "^6.0.2", "framer-motion": "^12.23.6", "graphql": "^16.11.0", "html-to-image": "^1.11.13", "lodash": "^4.17.21", "posthog-js": "^1.257.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-json-view": "^1.21.3", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "react-table": "^7.8.0", "react-window": "^1.8.11", "reactflow": "^11.11.4", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "scripts": {"start": "vite --port 3000 --open", "start:ee": "VITE_BUILD_EDITION=ee vite --port 3000 --open", "start:test": "NODE_ENV=test vite --port 3000 --open", "start:test:ee": "VITE_BUILD_EDITION=ee NODE_ENV=test vite --port 3000 --open", "build": "tsc -p tsconfig.ce.json && vite build", "build:ce": "tsc -p tsconfig.ce.json && vite build", "build:ee": "VITE_BUILD_EDITION=ee vite build", "test": "pnpm run test:setup && NODE_ENV=test pnpm cypress open; pnpm run test:cleanup", "test:headless": "pnpm run test:setup && NODE_ENV=test npx cypress run --browser chrome; pnpm run test:cleanup", "test:headless:chromium": "pnpm run test:setup && NODE_ENV=test npx cypress run --browser chromium; pnpm run test:cleanup", "test:setup": "bash ../dev/setup-e2e.sh", "test:cleanup": "bash ../dev/cleanup-e2e.sh", "test:ee": "pnpm run test:ee:setup && NODE_ENV=test pnpm cypress open; pnpm run test:ee:cleanup", "test:ee:headless": "pnpm run test:ee:setup && NODE_ENV=test npx cypress run --browser chrome; pnpm run test:ee:cleanup", "test:ee:headless:chromium": "pnpm run test:ee:setup && NODE_ENV=test npx cypress run --browser chromium; pnpm run test:ee:cleanup", "test:ee:setup": "bash ../ee/dev/setup-e2e.sh", "test:ee:cleanup": "bash ../ee/dev/cleanup-e2e.sh", "view:coverage": "npx nyc report --reporter=text-summary", "view:coverage:frontend": "npx nyc report --reporter=html", "view:coverage:backend": "cd ../core && go tool cover -html=coverage.out", "generate": "npx graphql-codegen", "generate:ce": "npx graphql-codegen --config codegen.ce.yml", "generate:ee": "npx graphql-codegen --config codegen.ee.yml"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.28.0", "@cypress/code-coverage": "^3.14.5", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@tailwindcss/postcss": "^4.1.11", "@types/lodash": "^4.17.20", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "babel-plugin-istanbul": "^7.0.0", "buffer": "^6.0.3", "cypress": "^14.5.2", "nyc": "^17.1.0", "postcss": "^8.5.6", "process": "^0.11.10", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.5"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}